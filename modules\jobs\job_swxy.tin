#NOP {守卫襄阳任务模块};
#ALIAS {jobgo_swxy} {
  on_swxy_before_go {gotonpc {吕文德} {startfull {jobask_swxy}}}
};
#NOP {接收守卫襄阳任务};
#ALIAS {jobask_swxy} {
  dohalt {jobask_swxy_ask}
};
#ALIAS {jobask_swxy_ask} {
  #CLASS jobrequestclass KILL;
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向吕文德打听有关『守卫襄阳』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass KILL;
    #CLASS jobresponseclass OPEN;
    #ACTION {^吕文德对你说道：不知道这次能不能守下来。} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobdo_swxy}
    };
    #ACTION {^吕文德对你说道：您上次任务辛苦了，还是先休息一下再说吧} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #IF {"$hp[lastjob]" == "守卫襄阳"} {
        dohalt {jobprepare}
      };
      #ELSE {
        dohalt {on_swxy_wait}
      };
    };
    #ACTION {^吕文德对你说道：我这里现在没有什么任务，你等会再来吧。|吕文德对你说道：镜像已经开启太多，欢迎下次参与。|您先歇口气再说话吧。} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {on_swxy_wait};
    };
    #ACTION {^吕文德说道：「我看你獐头鼠目，不象个好人，如何能放心把军机大事托付给你。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {goshen {zhengqi dan} {10000} {jobgo_swxy}};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  time;
  cond;
  jobtimes;
  ask lv wende about 守卫襄阳
};
#NOP {执行守卫襄阳};
#ALIAS {jobdo_swxy} {
  #VARIABLE {idle} {-900};
  #VARIABLE {workingflag} {2};
  #VARIABLE {wushiflag} {0};
  #VARIABLE {jobstart_ts} {@now{}};
  #VARIABLE {jobnpc_wave} {0};
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #ACTION {^你{速度太慢，蒙古武士已攻入玄武门，任务失败。|擅离职守，任务失败。}} {
    #CLASS jobfightclass KILL;
    #CLASS jobdoclass KILL;
    #VARIABLE {wushiflag} {2};
    #VARIABLE {jobnpc_wave} {0};
    joblog {任务失败。};
    #VARIABLE {workingflag} {0};
    #VARIABLE {idle} {0};
    dohalt {loc {jobprepare}}
  };
  #ACTION {^城外的山谷传来一声凄厉的长啸，令人毛骨悚然。|城外厮杀的声音嘈杂无比，襄阳城墙，虽然不是雄关如铁，但是和血肉之躯比较起来，似乎也强了不少。|城下的蒙古人顿时发出了一声狂热的叫喊声，像是潮水一样冒着守兵的箭矢和滚石檑木，急切的攀爬着云梯。} {
    #NOP {准备，1分钟倒计时};
    #NOP dohalt {startfull {yun qi;yun jing;yun jingli;heng;#VARIABLE {idle} {-900};} {2} {{timestamp}{@delayed{40}}}};
    #VARIABLE {idle} {-900};
  };
  #ACTION {^蒙古士兵翻越了城墙，撕开了守军一个又一个的防守点，你带着增援的兵卒，从驰道上冲上了城墙。} {
    #MATH {jobnpc_wave} {$jobnpc_wave + 1};
    joblog {第【@padLeft{{$jobnpc_wave}{2}{0}}】波蒙古武士开始进攻。};
    jobfight_swxy;
  };
  #ACTION {^恭喜你！你成功的完成了守卫襄阳任务！你被奖励了：} {
    #VARIABLE {jobreward_exp} {};
    #VARIABLE {jobreward_pot} {};
    #VARIABLE {jobreward_tongbao} {零};
    #VARIABLE {idle} {0};
    #VARIABLE {workingflag} {0};
    #VARIABLE {jobnpc_wave} {0};
    #VARIABLE {wushiflag} {1};
    #VARIABLE {lastjob} {守卫襄阳};
    #CLASS rewardclass OPEN;
    #ACTION {^%*点经验!} {
      #VARIABLE {jobreward_exp} {%%%1};
    };
    #ACTION {^%*枚情怀币！} {
      #VARIABLE {jobreward_tongbao} {%%%1};
    };
    #ACTION {^%*点潜能!} {
      #CLASS rewardclass KILL;
      #VARIABLE {jobreward_pot} {%%%1};      
      #VARIABLE __total_seconds @elapsed{$jobstart_ts};        
      #math __minutes {$__total_seconds / 60};     
      #math __seconds {$__total_seconds % 60}; 
      joblog {成功完成，获得【$jobreward_exp】点经验【$jobreward_pot】点潜能【$jobreward_tongbao】枚情怀币，耗时$__minutes分$__seconds秒。};
    };
    #CLASS jobfightclass KILL;
    #CLASS jobdoclass KILL;
    #VARIABLE {wushiflag} {2};
    #VARIABLE {jobnpc_wave} {0};    
    #VARIABLE {workingflag} {0};
    #VARIABLE {idle} {0};
    dohalt {jobprepare}
  };
  #CLASS jobdoclass CLOSE;
  joblog {开始守卫襄阳抗敌任务};
  createpfm {@getFightPerform{{守卫襄阳}}} {1} {menggu wushi} {menggu wushi 2};
  closesaving;
  closewimpy
};
#ALIAS {jobfight_swxy} {
  #VARIABLE {idle} {-900};
  #VARIABLE {jobfight_ts} {@now{}};
  #VARIABLE {jobnpc_wushi} {};
  #VARIABLE {jobnpc_wushi_name} {};
  #VARIABLE {jobnpc_wushi_id} {};  
  #CLASS jobfightclass KILL;
  #CLASS jobfightclass OPEN;
  #ACTION {^千夫长 %*(%*)} {
    #VARIABLE {jobnpc_wushi_name} {@lower{%%1}};
    #VARIABLE {jobnpc_wushi_id} {@lower{%%2}};     
    #CLASS jobfightcaptureclass KILL;
    #CLASS jobfightcaptureclass OPEN;
    #ACTION {^此人看上去师承%*，擅长使用%*伤敌} {
      #LOCAL {skill} {@trim{%%%2}};
      #LIST {common[dangersmyskills]} {find} {$skill} {danger}; 
      #IF {$danger == 0} {
        #LOCAL {danger} {999}
      };       
      #IF {&jobnpc_wushi[] == 0} {
        #VARIABLE {jobnpc_wushi[menggu wushi]} {
          {party} {%%%1}
          {skill} {%%%2}
          {danger} {$danger}
        };
        #VARIABLE {wushi_name1} {$jobnpc_wushi_name};
        #VARIABLE {wushi_id1} {@lower{%%2}};
      };
      #ELSE {
        #VARIABLE {jobnpc_wushi[menggu wushi 2]} {
          {party} {%%%1}
          {skill} {%%%2}
          {danger} {$danger}
        };
        #VARIABLE {wushi_name2} {$jobnpc_wushi_name};
        #VARIABLE {wushi_id2} {@lower{%%2}}; 
        #IF {$jobnpc_wushi[menggu wushi][danger] > $danger} {
          #VARIABLE {tmp_id} {$wushi_id1};
          #VARIABLE {wushi_id1} {$wushi_id2};
          #VARIABLE {wushi_id2} {$tmp_id};
          #VARIABLE {tmp_name}  {$wushi_name1};
          #VARIABLE {wushi_name1} {$wushi_name2};
          #VARIABLE {wushi_name2} {$tmp_name};
        };
      };
      joblog {第【@padLeft{{$jobnpc_wave}{2}{0}}】波发现来自【%%%1】使用【%%%2】的【$jobnpc_wushi_name】。};
    };
    #CLASS jobfightcaptureclass CLOSE;    
  };
  #ACTION {^{设定环境变量：action \= \"checkwushi\"|你设定checkwushi为反馈信息}} {
    createpfm {@getFightPerform{{守卫襄阳}}} {1} {$wushi_id1} {$wushi_id2};
    joblog {第【@padLeft{{$jobnpc_wave}{2}{0}}】波优先击杀【$wushi_name1】};
    #VARIABLE {echots} {0};
    #CLASS jobfightcaptureclass KILL;    
    openwimpy;
    autopfm
  };
  #ACTION {^%*「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    #VARIABLE {workingflag} {2};
    kill menggu wushi;    
    #NOP 定义触发器开关变量，初始为 1（允许触发）;
    #VARIABLE {jobfight_action_enabled} {1};
    #ACTION {^这里没有这个人。} {
      #NOP 立即关闭触发器开关，防止重复执行;
      #IF {$jobfight_action_enabled == 1} {        
        #VARIABLE {jobfight_action_enabled} {0};        
        #CLASS jobfightclass KILL;
        joblog {第【@padLeft{{$jobnpc_wave}{2}{0}}】波蒙古武士已被消灭，耗时【@eval{@now{} - $jobfight_ts}】秒。};
        stopfight;
        dohalt {
          get gold from corpse;
          get gold from corpse 2;
          #IF {$jobnpc_wave < 9} {
            jobrest_swxy;
          };
        };
      };
    };
  };
  #NOP {自己晕了，检查身体去下一个任务};
  #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉} {
    #VARIABLE {idle} {0};
    dohalt {jobprepare}    
  } {1};
  #CLASS jobfightclass CLOSE;  
  startfight;
  closewimpy;
  look menggu wushi;
  kill menggu wushi;
  look menggu wushi 2;
  kill menggu wushi 2;
  echo {checkwushi}
  
};
#NOP {武士间隙休息打坐，最多1分钟};
#ALIAS {jobrest_swxy} {  
  dohalt {forceheal {startfull {yun qi;yun jing;yun jingli;heng;createpfm {@getFightPerform{{守卫襄阳}}} {1} {menggu wushi} {menggu wushi 2};#VARIABLE {idle} {-900};} {2} {{timestamp}{@delayed{40}}}}};
};