#NOP {颂摩崖任务模块};
#ALIAS {jobgo_smy} {
  on_smy_before_go {gotonpc {鲁有脚} {startfull {jobask_smy}}}
};
#NOP {接收颂摩崖任务};
#ALIAS {jobask_smy} {
  dohalt {jobask_smy_ask}
};
#ALIAS {jobask_smy_ask} {
  #CLASS jobrequestclass KILL;
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向鲁有脚打听有关『报效国家』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass KILL;
    #CLASS jobresponseclass OPEN;
    #ACTION {^鲁有脚说道：「颂摩崖是西夏武士东来的必经之地，你速带几名弟子埋伏在那里截杀。} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {gotodo {星宿海} {颂摩崖} {jobdo_smy}}
    };
    #ACTION {^鲁有脚说道：「您上次任务辛苦了，还是先休息一下再说吧} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #IF {"$hp[lastjob]" == "颂摩崖"} {
        dohalt {jobprepare}
      };
      #ELSE {
        dohalt {on_smy_wait}
      };
    };
    #ACTION {^鲁有脚说道：「我这里现在没有什么任务，你等会再来吧。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {on_smy_wait};
    };
    #ACTION {^鲁有脚说道：「我看你獐头鼠目，不象个好人，如何能放心把军机大事托付给你。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {goshen {zhengqi dan} {10000} {jobgo_smy}};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  time;
  cond;
  jobtimes;
  ask lu youjiao about 报效国家
};
#NOP {执行颂摩崖};
#ALIAS {jobdo_smy} {
  #VARIABLE {idle} {-900};
  #VARIABLE {workingflag} {2};
  #VARIABLE {wushiflag} {0};
  #VARIABLE {jobstart_ts} {@now{}};
  #VARIABLE {jobnpc_wave} {0};
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #ACTION {^你{速度太慢，西夏武士已过颂摩崖|擅离职守，任务失败}} {
    #CLASS jobfightclass KILL;
    #CLASS jobdoclass KILL;
    #VARIABLE {wushiflag} {2};
    #VARIABLE {jobnpc_wave} {0};
    joblog {任务失败。};
    #VARIABLE {workingflag} {0};
    #VARIABLE {idle} {0};
    dohalt {loc {jobprepare}}
  };
  #ACTION {^远处的山路传来一阵轻啸，隐约听得有人施展轻功飞驰而来} {
    #NOP {准备，1分钟倒计时};    
    #VARIABLE {idle} {-900};
  };
  #ACTION {^山崖北面的小路上闪出两条人影，你纵身而起，立即和人影战在了一起。} {
    #MATH {jobnpc_wave} {$jobnpc_wave + 1};
    #VARIABLE {wushi_id1} {};
    #VARIABLE {wushi_id2} {};    
    joblog {第【@padLeft{{$jobnpc_wave}{2}{0}}】波西夏武士开始进攻。};
    jobfight_smy;
  };
  #ACTION {^恭喜你！你成功的完成了颂摩崖任务！你被奖励了：} {
    #VARIABLE {jobreward_exp} {};
    #VARIABLE {jobreward_pot} {};
    #VARIABLE {jobreward_tongbao} {零};
    #VARIABLE {idle} {0};
    #VARIABLE {workingflag} {0};
    #VARIABLE {jobnpc_wave} {0};
    #VARIABLE {wushiflag} {1};
    #VARIABLE {lastjob} {颂摩崖};
    #CLASS rewardclass OPEN;
    #ACTION {^%*点经验!} {
      #VARIABLE {jobreward_exp} {%%%1};
    };
    #ACTION {^%*枚情怀币！} {
      #VARIABLE {jobreward_tongbao} {%%%1};
    };
    #ACTION {^%*点潜能!} {
      #CLASS rewardclass KILL;
      #VARIABLE {jobreward_pot} {%%%1};      
      #VARIABLE __total_seconds @elapsed{$jobstart_ts};        
      #math __minutes {$__total_seconds / 60};     
      #math __seconds {$__total_seconds % 60};    
      joblog {成功完成，获得【$jobreward_exp】点经验【$jobreward_pot】点潜能【$jobreward_tongbao】枚情怀币，耗时$__minutes分$__seconds秒。};      
    };
    #CLASS jobfightclass KILL;
    #CLASS jobdoclass KILL;
    #VARIABLE {wushiflag} {2};
    #VARIABLE {jobnpc_wave} {0};    
    #VARIABLE {workingflag} {0};
    #VARIABLE {idle} {0};
    dohalt {jobprepare}
  };
  #CLASS jobdoclass CLOSE;
  joblog {开始颂摩崖抗敌任务};
  #DELAY {2} {
    createpfm {@getFightPerform{{颂摩崖}}} {1} {xixia wushi} {xixia wushi 2};
    closesaving;
    closewimpy
  };
};
#ALIAS {jobfight_smy} {
  #VARIABLE {idle} {-900};
  #VARIABLE {jobfight_ts} {@now{}};
  #VARIABLE {jobnpc_wushi} {};
  #VARIABLE {jobnpc_wushi_name} {};
  #VARIABLE {jobnpc_wushi_id} {};  
  #CLASS jobfightclass KILL;
  #CLASS jobfightclass OPEN;
  #ACTION {^西夏一品堂武士 %*(%*)} {
    #VARIABLE {jobnpc_wushi_name} {@lower{%%1}};
    #VARIABLE {jobnpc_wushi_id} {@lower{%%2}};            
    #CLASS jobfightcaptureclass KILL;
    #CLASS jobfightcaptureclass OPEN;
    #ACTION {^此人看上去师承%*，擅长使用%*伤敌} {
      #LOCAL {skill} {@trim{%%%2}};
      #NOP {查找预设技能排序索引,索引值越低危险越高};
      #LIST {common[dangersmyskills]} {find} {$skill} {danger};
      #IF {$danger == 0} {
        #LOCAL {danger} {999}
      };       
      #IF {&jobnpc_wushi[] == 0} {
        #VARIABLE {jobnpc_wushi[xixia wushi]} {
          {party} {%%%1}
          {skill} {%%%2}
          {danger} {$danger}
        };
        #VARIABLE {wushi_name1} {$jobnpc_wushi_name};
        #VARIABLE {wushi_id1} {@lower{%%2}};
      };
      #ELSE {
        #VARIABLE {jobnpc_wushi[xixia wushi 2]} {
          {party} {%%%1}
          {skill} {%%%2}
          {danger} {$danger}
        };
        #VARIABLE {wushi_name2} {$jobnpc_wushi_name};
        #VARIABLE {wushi_id2} {@lower{%%2}}; 
        #IF {$jobnpc_wushi[xixia wushi][danger] > $danger} {
          #VARIABLE {tmp_id} {$wushi_id1};
          #VARIABLE {wushi_id1} {$wushi_id2};
          #VARIABLE {wushi_id2} {$tmp_id};
          #VARIABLE {tmp_name}  {$wushi_name1};
          #VARIABLE {wushi_name1} {$wushi_name2};
          #VARIABLE {wushi_name2} {$tmp_name};
        };
      };
      joblog {第【@padLeft{{$jobnpc_wave}{2}{0}}】波发现来自【%%%1】使用【%%%2】的【$jobnpc_wushi_name】。};
    };
    #CLASS jobfightcaptureclass CLOSE;
    
  };
  #ACTION {^{设定环境变量：action \= \"checkwushi\"|你设定checkwushi为反馈信息}} {
    createpfm {@getFightPerform{{颂摩崖}}} {1} {$wushi_id1} {$wushi_id2};
    #VARIABLE {echots} {0};
    #CLASS jobfightcaptureclass KILL;    
    joblog {第【@padLeft{{$jobnpc_wave}{2}{0}}】波优先击杀【$wushi_name1】};
    openwimpy;
    autopfm
  };
  #ACTION {^%*「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    #VARIABLE {workingflag} {2};
    kill xixia wushi;    
    #NOP 定义触发器开关变量，初始为 1（允许触发）;
    #VARIABLE {jobfight_action_enabled} {1};
    #ACTION {^这里没有这个人。} {
      #NOP 立即关闭触发器开关，防止重复执行;
      #IF {$jobfight_action_enabled == 1} {        
        #VARIABLE {jobfight_action_enabled} {0};        
        #CLASS jobfightclass KILL;
        joblog {第【@padLeft{{$jobnpc_wave}{2}{0}}】波西夏武士已被消灭，耗时【@eval{@now{} - $jobfight_ts}】秒。};
        stopfight;
        dohalt {
          get gold from corpse;
          get gold from corpse 2;
          #IF {$jobnpc_wave < 14} {
            jobrest_smy;
          };
        };
      };
    };
  };
  #NOP {自己晕了，检查身体去下一个任务};
  #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉} {
    #VARIABLE {idle} {0};
    dohalt {jobprepare}
  } {1};
  #CLASS jobfightclass CLOSE;     
  startfight;
  closewimpy;
  look xixia wushi;
  kill xixia wushi;
  look xixia wushi 2;
  kill xixia wushi 2;
  echo {checkwushi}  
};
#NOP {武士间隙休息打坐，最多1分钟};
#ALIAS {jobrest_smy} {  
  dohalt {forceheal {startfull {yun qi;yun jing;yun jingli;heng;createpfm {@getFightPerform{{颂摩崖}}} {1} {xixia wushi} {xixia wushi 2};#VARIABLE {idle} {-900};} {2} {{timestamp}{@delayed{40}}}}};
  
};