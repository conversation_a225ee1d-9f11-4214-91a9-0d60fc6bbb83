#NOP {情怀岛副本模块};
#ALIAS {jobgo_qhd} {
    #VARIABLE {currentjob} {情怀岛};
    #IF {@carryqty{da huandan} <= 1} {
      tbbuy {da huandan} {2} {gotodo {扬州城}{车马行} {startfull {jobqhd_start}}}
    };
    #ELSE {
      gotodo {扬州城}{车马行} {startfull {jobqhd_start}}
    }
};

#NOP {情怀岛副本开始};
#ALIAS {jobqhd_start} {
    #CLASS jobaskclass KILL;
    #CLASS jobaskclass OPEN;
    #VARIABLE {idle} {0};
    #VARIABLE {jobstart_ts} {@now{}};
    #VARIABLE {qhd_phase} {1}; #NOP {1:准备阶段, 2:航行阶段, 3:战斗阶段};
    #VARIABLE {qhd_repair_count} {0};
    #VARIABLE {qhd_pirate_count} {0};
    #VARIABLE {qhd_master_killed} {0};
    #VARIABLE {qhd_ship_health} {1000};
    #VARIABLE {qhd_target_x} {0};
    #VARIABLE {qhd_target_y} {0};
    #VARIABLE {qhd_current_x} {0};
    #VARIABLE {qhd_current_y} {0};
    
    #ACTION {^请明天再来执行血海夺岛任务吧} {
        #CLASS jobaskclass KILL;
        joblog {情怀岛副本间隔未满24小时} {情怀岛};
        #NOP {标记副本失败并使用副本系统的结束流程};
        fubenfail {情怀岛};
        dohalt {fuben_end {jobprepare}}
    };

    #NOP {设置副本超时保护，120分钟后自动结束};
    #TICKER {qhd_timeout} {
        joblog {情怀岛副本超时，自动结束} {情怀岛};
        #UNTICKER {qhd_timeout};
        #UNTICKER {qhd_repair};
        #UNTICKER {qhd_location};
        #UNTICKER {qhd_move};
        #CLASS jobaskclass KILL;
        #CLASS jobdoclass KILL;
        #CLASS jobnavclass KILL;
        #CLASS jobfightclass KILL;
        #CLASS jobfinishclass KILL;
        fubenfail {情怀岛};
        dohalt {fuben_end {jobprepare}};
    } {7200};

    #CLASS jobaskclass CLOSE;
    
    qinghuaidao;
    createpfm {$conf[pfm][scene][qhd]} {1};
    joblog {开始情怀岛副本挑战} {情怀岛};
    set wimpy 100;
    #DELAY {3} {
        jobqhd_prepare;        
    };
};

#NOP {情怀岛准备阶段};
#ALIAS {jobqhd_prepare} {
    #NOP {设置技能和装备};
    #IF {"$conf[pfm][scene][qhd]" != ""} {
        createpfm {$conf[pfm][scene][qhd]} {1};
    };
    #ELSE {
        createpfm {$conf[pfm][scene][normal]} {1};
    };
    set wimpycmd hp;
    joblog {情怀岛副本准备完成，开始挑战} {情怀岛};
    #NOP {调用主要逻辑};
    jobqhd_do;
    jump;
};

#NOP {情怀岛主要逻辑};
#ALIAS {jobqhd_do} {
    #CLASS jobdoclass KILL;
    #CLASS jobdoclass OPEN;
    
    #NOP {被传送到海滩的触发器};
    #ACTION {^你被秘密地带到了神秘的小海滩！} {
        #VARIABLE {qhd_phase} {2};
        joblog {到达神秘海滩，准备登船} {情怀岛};
        #DELAY {2} {
            climb
        }
    };
    
    #NOP {登船成功的触发器};
    #ACTION {^你爬上了情怀战舰，浑身湿漉漉的！} {
        joblog {成功登上情怀战舰} {情怀岛};
        #VARIABLE {qhd_phase} {2};
        jobqhd_navigation;
        location;
    };
    
    #NOP {舰船损坏的触发器};
    #ACTION {^舰底传来"咚咚"地声音，应该是海盗破坏了情怀舰，} {
        joblog {战舰受损，需要下舱修理} {情怀岛};
        #DELAY {1} {
            down
        }
    };
    
    #NOP {这里没有人的触发器};
    #ACTION {^这里没有这个人。} {
        climb
    };
    
    #NOP {战舰牢固度检查};
    #ACTION {^情怀战舰的牢固度：(.*)，该值低于500战舰有倾} {
        #VARIABLE {qhd_ship_health} {%1};
        #IF {$qhd_ship_health < 800} {
            joblog {战舰牢固度过低($qhd_ship_health)，开始修理} {情怀岛};
            #TICKER {qhd_repair} {
                repair
            } {2};
        };
        #ELSE {
            #UNTICKER {qhd_repair};
            joblog {战舰牢固度正常($qhd_ship_health)} {情怀岛};
        }
    };
    
    #NOP {海盗出现的触发器};
    #ACTION {^海盗 *(.*)} {
        joblog {发现海盗，开始战斗} {情怀岛};
        kill pirate 1;
        kill pirate 2;
        #MATH {qhd_pirate_count} {$qhd_pirate_count + 1};
    };
    
    #NOP {简单测试触发器};
    #ACTION {跳上} {
        joblog {=== 触发器测试：检测到"跳上" ===} {情怀岛};
        joblog {完整文本：%0} {情怀岛};
    };

    #ACTION {情怀} {
        joblog {=== 触发器测试：检测到"情怀" ===} {情怀岛};
        joblog {完整文本：%0} {情怀岛};
    };

    #NOP {跳上战舰的触发器};
    #ACTION {跳上了情怀舰} {
        joblog {=== 成功触发：跳上了情怀舰 ===} {情怀岛};
        #VARIABLE {qhd_phase} {2};
        jobqhd_navigation;
        jobqhd_fight;
        jobqhd_finish;
        #TICKER {qhd_location} {
            location
        } {3};
        location;
    };
    
    #NOP {HP状态检查};
    #ACTION {^HP\:(.+)} {
        #VARIABLE {idle} {0};
        #IF {"$hp[qi_per]" != "" && $hp[qi_per] < 80} {
            fu da huandan;
            joblog {血量过低，使用大还丹恢复} {情怀岛};
        };
        #IF {"$hp[neili]" != "" && $hp[neili] < 5000} {
            fu da huandan;
            joblog {内力不足，使用大还丹恢复} {情怀岛};
        };
    };
    
    #NOP {不要关闭 jobdoclass，让触发器保持活跃};
    #VARIABLE {idle} {0};
    #NOP {启动状态检查};
    jobqhd_check;
};

#NOP {情怀岛导航逻辑};
#ALIAS {jobqhd_navigation} {
    #CLASS jobnavclass KILL;
    #CLASS jobnavclass OPEN;
    
    #NOP {简单坐标测试};
    #ACTION {情怀岛坐标是} {
        joblog {=== 检测到坐标信息：%0 ===} {情怀岛};
    };

    #NOP {更宽松的坐标解析};
    #ACTION {情怀岛坐标是：.*当前坐标是：} {
        joblog {=== 匹配到坐标行：%0 ===} {情怀岛};
        #REGEXP {%0} {情怀岛坐标是：\(([0-9]+), ([0-9]+)\)，当前坐标是：\(([0-9]+), ([0-9]+)\)} {
            #VARIABLE {qhd_target_x} {&1};
            #VARIABLE {qhd_target_y} {&2};
            #VARIABLE {qhd_current_x} {&3};
            #VARIABLE {qhd_current_y} {&4};
            joblog {目标坐标：(&1, &2)，当前坐标：(&3, &4)} {情怀岛};
            jobqhd_move;
        };
    };

    #NOP {原始坐标信息解析 - 备用};
    #ACTION {^情怀岛坐标是：\(([0-9]+), ([0-9]+)\)，当前坐标是：\(([0-9]+), ([0-9]+)\)。} {
        #VARIABLE {qhd_target_x} {%1};
        #VARIABLE {qhd_target_y} {%2};
        #VARIABLE {qhd_current_x} {%3};
        #VARIABLE {qhd_current_y} {%4};

        joblog {解析坐标：目标($qhd_target_x,$qhd_target_y) 当前($qhd_current_x,$qhd_current_y)} {情怀岛};

        #NOP {坐标解析完成，开始导航};
        
        #LOCAL {direction} {};
        #IF {$qhd_current_x < $qhd_target_x} {
            #VARIABLE {direction} {right};
        };
        #ELSEIF {$qhd_current_x > $qhd_target_x} {
            #VARIABLE {direction} {left};
        };
        #ELSEIF {$qhd_current_y < $qhd_target_y} {
            #VARIABLE {direction} {up};
        };
        #ELSEIF {$qhd_current_y > $qhd_target_y} {
            #VARIABLE {direction} {down};
        };
        
        #IF {"$direction" != ""} {
            joblog {导航：从($qhd_current_x,$qhd_current_y)前往($qhd_target_x,$qhd_target_y)，方向：$direction} {情怀岛};
            #TICKER {qhd_move} {
                order $direction
            } {2};
        };
        #ELSE {
            joblog {已到达目标坐标，停止导航} {情怀岛};
            #UNTICKER {qhd_move};
        };
    };
    
    #NOP {不要关闭 jobnavclass，让导航触发器保持活跃};
};

#NOP {情怀岛战斗逻辑};
#ALIAS {jobqhd_fight} {
    #CLASS jobfightclass KILL;
    #CLASS jobfightclass OPEN;
    
    #NOP {海盗头目出现};
    #ACTION {^(.*)海盗头目\s+(\S+)\((.*)\)} {
        #VARIABLE {qhd_master_name} {%2};
        #VARIABLE {qhd_master_id} {%3};
        #FORMAT {qhd_master_id} {%l} {$qhd_master_id};
        joblog {发现海盗头目：$qhd_master_name($qhd_master_id)} {情怀岛};
        look $qhd_master_id;
        
        #ACTION {^这位海盗头目来自(.*)。} {
            #VARIABLE {qhd_master_party} {%1};
            joblog {海盗头目门派：$qhd_master_party} {情怀岛};
            #NOP {检查是否需要放弃};
            #IF {"$conf[qhd_abandon_parties]" != "" && @contains{{$conf[qhd_abandon_parties]}{$qhd_master_party}}} {
                joblog {遇到放弃门派($qhd_master_party)，结束副本} {情怀岛};
                leave;
                jobqhd_cleanup;
                #NOP {标记副本失败并使用副本系统的结束流程};
                fubenfail {情怀岛};
                dohalt {fuben_end {jobprepare}};
            };
            #ELSE {
                joblog {开始挑战海盗头目} {情怀岛};
                #DELAY {1} {
                    createpfm {$conf[pfm][scene][qhd_boss]} {1};
                    #DELAY {2} {
                        yun qi;
                        yun jing;
                        yun jingli;
                        #DELAY {1} {
                            kill $qhd_master_id
                        };
                    };
                };
            };
        };
    };
    
    #NOP {战斗结束};
    #ACTION {^(\S+)(?:「啪」的一声倒在地上，挣扎着抽动了几下就死了。|一个闪身就不见了。)} {
        #IF {"$qhd_master_name" != "" && "%1" == "$qhd_master_name"} {
            joblog {成功击败海盗头目：$qhd_master_name} {情怀岛};
            #VARIABLE {qhd_master_killed} {1};
            #DELAY {2} {
                leave
            };
        };
        #ELSE {
            joblog {击败海盗：%1} {情怀岛};
            kill pirate;
        };
    };
    
    #NOP {死亡处理};
    #ACTION {^你「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
        joblog {挑战情怀岛失败，角色死亡} {情怀岛};
        jobqhd_cleanup;
        #NOP {标记副本失败并使用副本系统的结束流程};
        fubenfail {情怀岛};
        dohalt {fuben_end {jobprepare}};
    };
    
    #NOP {不要关闭 jobfightclass，让战斗触发器保持活跃};
};

#NOP {情怀岛完成处理};
#ALIAS {jobqhd_finish} {
    #CLASS jobfinishclass KILL;
    #CLASS jobfinishclass OPEN;

    #NOP {获得情怀币奖励};
    #ACTION {^你得到了(.*)枚情怀币} {
        #VARIABLE {qhd_reward} {%1};
        joblog {情怀岛副本完成！获得$qhd_reward枚情怀币} {情怀岛};
        #LOCAL {elapsed_time} {@elapsed{$jobstart_ts}};
        joblog {副本用时：$elapsed_time 秒} {情怀岛};
        jobqhd_cleanup;
        #NOP {标记副本成功并使用副本系统的结束流程};
        fubensuccess {情怀岛};
        dohalt {fuben_end {jobprepare}};
    };

    #NOP {挑战成功信息};
    #ACTION {^血海夺岛成功,你成功获得(.*)} {
        joblog {血海夺岛成功！获得奖励：%1} {情怀岛};
    };

    #NOP {意外撤退};
    #ACTION {^(乘坐救生小艇你一路回到了长江|幸好你战前准备充分，乘坐救)} {
        joblog {情怀岛副本意外撤退} {情怀岛};
        jobqhd_cleanup;
        #NOP {标记副本失败并使用副本系统的结束流程};
        fubenfail {情怀岛};
        dohalt {fuben_end {jobprepare}};
    };

    #NOP {经过长时间海上旅行};
    #ACTION {^经过长时间的海上旅行，战舰} {
        joblog {海上旅行结束，准备登岛} {情怀岛};
        #DELAY {1} {
            out;
            jobqhd_fight;
        };
    };

    #NOP {到达情怀岛};
    #ACTION {^情怀岛$} {
        joblog {成功到达情怀岛！} {情怀岛};
        #DELAY {1} {
            createpfm {$conf[pfm][scene][qhd_boss]} {1};
            look;
        };
    };

    #NOP {不要关闭 jobfinishclass，让完成触发器保持活跃};
};

#NOP {情怀岛状态检查 - 简化版本};
#ALIAS {jobqhd_check} {
    #VARIABLE {idle} {0};
    joblog {当前阶段：$qhd_phase} {情怀岛};
    #NOP {定期检查，确保副本正常运行};
    #DELAY {5} {
        #IF {"$currentjob" == "情怀岛"} {
            jobqhd_check
        }
    }
};

#NOP {情怀岛清理函数};
#ALIAS {jobqhd_cleanup} {
    #UNTICKER {qhd_timeout};
    #UNTICKER {qhd_repair};
    #UNTICKER {qhd_location};
    #UNTICKER {qhd_move};
    #CLASS jobaskclass KILL;
    #CLASS jobdoclass KILL;
    #CLASS jobnavclass KILL;
    #CLASS jobfightclass KILL;
    #CLASS jobfinishclass KILL;
    joblog {情怀岛副本清理完成} {情怀岛};
};

#NOP {情怀岛强制停止};
#ALIAS {jobqhd_halt} {
    joblog {强制停止情怀岛副本} {情怀岛};
    jobqhd_cleanup;
    fubenfail {情怀岛};
    dohalt {fuben_end {jobprepare}};
};